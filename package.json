{"name": "kadayapkge", "version": "1.0.0", "description": "An Electron application with Vue and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "chcp 65001 && electron-vite dev", "test": "electron-vite dev --mode test", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "@mediapipe/tasks-vision": "^0.10.22-rc.20250304", "electron-updater": "^6.3.9", "face-api.js": "^0.22.2", "gsap": "^3.12.7", "three": "^0.174.0", "vite-plugin-mkcert": "^1.17.8", "vue-router": "^4.5.0"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^22.13.4", "@vitejs/plugin-vue": "^5.2.1", "electron": "^34.2.0", "electron-builder": "^25.1.8", "electron-vite": "^3.0.0", "eslint": "^9.20.1", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.5.1", "typescript": "^5.7.3", "vite": "^6.1.0", "vue": "^3.5.13", "vue-tsc": "^2.2.2"}}