<template>
  <div class="udp-message-container">
    <div class="message-input">
      <!-- <input 
        v-model="message" 
        type="text" 
        placeholder="请输入要发送的消息"
        @keyup.enter="sendMessage"
      >
      <button @click="sendMessage" :disabled="!message">发送</button> -->
      <button @click="sendText('start_shooting')" >开始</button>
      <button @click="sendText('next_shooting')" >下一个</button>
      <button @click="sendText('stop_shooting')" >停止</button>
      <button @click="captureImageFromBox(10,10,200,200)">截图测试</button>
    </div>

    <div class="message-display">
      <div>接收到的消息</div>
      <div class="message-content" v-if="receivedMsg.content">
        <div>内容：{{ receivedMsg.content }}</div>
        <div class="timestamp">时间：{{ receivedMsg.timestamp }}</div>
      </div>
      <p v-else>暂无消息</p>
    </div>
    
    <!-- 添加Person组件 -->
    <person ref="personRef"  />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject } from 'vue'
// import { invoke } from '@tauri-apps/api/core'
// import { listen } from '@tauri-apps/api/event'
import Person from './person.vue'  // 导入Person组件

const personRef = ref(null);  // 添加对Person组件的引用
const message = ref('')
const receivedMsg = ref({
  content: '',
  timestamp: ''
})

// 使用inject获取父组件提供的changeStatus方法
const changeStatus = inject('changeStatus')

const sendText = async (value: string) =>{
  message.value = value
  sendMessage()
}

setTimeout(() => {
  sendText('start_shooting')
}, 1500);
setTimeout(() => {
  sendText('start_shooting')
}, 20000);
// 在 sendMessage 函数中
const sendMessage = async () => {
  if (!message.value) return
  try {
    console.log('发送消息:', message.value)
    let result = await window.api?.sendUdpMessage(message.value)
    console.log(result)
    message.value = ''
  } catch (error) {
    console.error('发送消息失败:', error)
  }
}

let unlisten: (() => void) | undefined
let unlistenStatus: (() => void) | undefined

type status = 'idle'|'success' | 'error' |'start' | 'catch' | 'shooting' | 'discorver'
let now_status :status = 'idle'

// 修改changeStatus实现，直接调用inject获取的方法
// 删除原有的changeStatus定义，改为：
// 不再需要provide('changeStatus', changeStatus)，因为我们现在使用的是父组件提供的方法

let checkOk = 0
let checkTimeout = 0
// 在 onMounted 中添加状态更新事件监听
onMounted(() => {
  // 监听 UDP 消息事件
  window.electron?.ipcRenderer?.on('udp-message', async (_, data) => {
    receivedMsg.value = data
    console.log('接收到 UDP 消息:', data.content)
    
    const content = data.content;
    
    let hasFace = false
    // 解析消息内容
    if (content.startsWith('faces_coord:')) {
      // 检测到人脸坐标信息
      const coordStr = content.substring('faces_coord:'.length);
      try {
        // 解析坐标字符串 [-654.28,-1712.69,1783.03]
        const coords = coordStr.replace('[', '').replace(']', '').split(',').map(Number);
        console.log('检测到人脸，3D坐标:', coords);
        changeStatus('shooting')
        // 这里可以添加检测到人脸时的处理逻辑
      } catch (e) {
        console.error('解析faces_coord失败:', e);
      }
    } else if (content.startsWith('face_box:')) {
      let paizhao = async (content)=>{
        // 检测到人脸框信息
        const boxStr = content.substring('face_box:'.length);
        try {
          // 解析坐标字符串 221,397,666,836 (左上x,左上y,右下x,右下y)
          const [x1, y1, x2, y2] = boxStr.split(',').map(Number);
          console.log('人脸框坐标:', { x1, y1, x2, y2 });
          
          
          // 这里可以添加根据人脸框截图的逻辑
          hasFace = await captureImageFromBox(x1, y1, x2, y2);
          if (hasFace) {
            changeStatus('catch')
            // 发送next_shooting
            sendText('next_shooting');
            checkOk = 0
            if (checkTimeout){
              clearTimeout(checkTimeout)
            }
          }
        } catch (e) {
          console.error('解析face_box失败:', e);
        }
      }
      if (checkOk === 0){
        checkOk = 1
        setTimeout(() => {
          checkOk = 2
          // checkTimeout = setTimeout(() => {
          //   if (checkOk==2){
              
              
          //   }
            
          // }, 1000);
        }, 4000);
        return
      }else if(checkOk === 1){
        
        return
      }else if(checkOk === 2){
        paizhao(content) 
        // checkOk = 0
        
      }
      
      
    }
  })
  
  // 添加状态更新事件监听
  window.electron?.ipcRenderer?.on('status-update', (_, status) => {
    console.log('收到状态更新:', status)
    changeStatus(status)
  })
})

// 根据人脸框坐标从摄像头截图的函数
// 修改截图函数
const captureImageFromBox = async (x1: number, y1: number, x2: number, y2: number) => {
  let result = false
  if (personRef.value) {
    // 获取图片
    let base64Image = (personRef.value as any).captureImageFromBox(x1, y1, x2, y2);
    if (base64Image) {
      // 检测是否包含人脸
      const hasFace = await (personRef.value as any).testFace(base64Image);
      
      if (hasFace) {
        result = true
        console.log('检测到人脸，保存图片');
        // 发送到主进程保存
        try {
          // const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          
          const timestamp = String(new Date().getTime());
          // const result = await window.api.saveFaceImage({
          //   filename: `face_${timestamp}.jpg`,
          //   data: base64Image
          // });
//         // 使用 window.api.greet 替代 invoke
          await window.api.greet({ 
            time: timestamp,
            img: base64Image
          });
          // (personRef.value as any).saveFaceBoxImage(x1, y1, x2, y2)
          await window.api.saveFaceImage({
            filename: `face_${timestamp}.jpg`,
            data: base64Image
          })
          console.log('图片保存成功');
          sendText('next_shooting');
        } catch (error) {
          console.error('保存图片失败:', error);
        }
      } else {
        console.log('未检测到人脸，跳过保存');
        sendText('next_shooting');
      }
    }
  }
  return result
};
// base64Image = ''
    
//     if (base64Image) {
//       // base64Image = 'data:' + base64Image
//       console.log('成功截取人脸图像');
//       console.log(base64Image)
//       // 可以将base64图像发送到后端
//       try {
//         const timestamp = String(new Date().getTime());
//         // 使用 window.api.greet 替代 invoke
//         await window.api.greet({ 
//           time: timestamp,
//           img: base64Image
//         });
//         console.log('图像数据已发送到 HTTP 服务');
//       } catch (error) {
//         console.error('发送图像到后端失败:', error);
//       }
//     }
//   } else {
//     console.error('Person组件未初始化');
//   }
// }

onUnmounted(() => {
  // 移除事件监听器
  window.electron?.ipcRenderer?.removeAllListeners('udp-message');
  window.electron?.ipcRenderer?.removeAllListeners('status-update');
})
</script>

<style scoped>
.udp-message-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.message-input {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.message-input input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.message-input button {
  padding: 8px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.message-input button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.message-display {
  padding: 5px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.message-content {
  background-color: #f9f9f9;
  padding: 2px;
  border-radius: 4px;
}

.timestamp {
  color: #666;
  font-size: 0.9em;
}
</style>