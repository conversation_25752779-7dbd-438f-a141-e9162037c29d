import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// 自定义 API 对象
const api = {
  // 发送 UDP 消息
  sendUdpMessage: (message: string) => ipcRenderer.invoke('send_udp_message', { message }),
  // 发送图像数据到 HTTP 服务
  greet: (data: { time: string, img: string }) => ipcRenderer.invoke('greet', data),
  saveFaceImage: (params) => {
    return ipcRenderer.invoke('save-face-image', params)
  },
  // 获取音频文件列表
  getAudioFiles: () => ipcRenderer.invoke('get-audio-files')
}

// 使用上下文隔离时，将 API 暴露给渲染进程
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    // 在现有的 contextBridge.exposeInMainWorld 中添加 saveFaceImage 方法
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (定义在 window 上)
  window.electron = electronAPI
  // @ts-ignore
  window.api = api
}
