<template>

  <div class="camera-container">
    <video ref="videoGRef" class="camera-video" autoplay playsinline></video>
    <video ref="videoRef" class="camera-video" autoplay playsinline></video>
    <canvas ref="canvasRef" class="detection-canvas"></canvas>
    <canvas ref="faceBoxCanvasRef" class="face-box-canvas"></canvas>

    <div class="camera-controls">
      <!-- <button @click="takeSnapshot" class="snapshot-btn">拍照</button>
        <button @click="switchCamera" class="switch-btn" v-if="hasMutipleCamera">切换摄像头</button> -->
    </div>

    <div v-if="imgUrl" class="snapshot-container">
      <img :src="imgUrl" class="snapshot-img" />
      <div class="snapshot-actions">
        <button @click="downloadImage" class="download-btn">下载</button>
        <button @click="closeSnapshot" class="close-btn">关闭</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, onUnmounted, defineExpose, inject } from 'vue';
import { FilesetResolver, ObjectDetector, FaceDetector } from '@mediapipe/tasks-vision';
import * as faceapi from 'face-api.js'

const videoRef = ref(null);
const videoGRef = ref(null);
const canvasRef = ref(null);
const faceBoxCanvasRef = ref(null); // 新增用于绘制人脸框的canvas引用
let animationFrameId = null;
let objectDetector = null;
let facedetector = null;
let faceDetector = null;
let nextRenderTimeout = null

let changeStatus = inject('changeStatus')
onMounted(async () => {
  // 直接初始化 MediaPipe
  await initMediaPipe();
  await initFaceApi()

  // 开始视频检测循环
  startDetection();
});

const initFaceApi = async () => {
  // await changeFaceDetector(SSD_MOBILENETV1)
  await faceapi.loadAgeGenderModel('/faceapi')
  await faceapi.loadTinyFaceDetectorModel('/faceapi')
  await faceapi.loadFaceLandmarkModel('/faceapi')

}
const runFaceApi = async () => {
  // await faceapi.nets.ageGenderNet.load('/faceapi')
  if(videoGRef.value){
    let result = await faceapi.detectAllFaces(videoGRef.value, new faceapi.TinyFaceDetectorOptions()).withFaceLandmarks().withAgeAndGender()
    return result
  }
 
}

// 初始化 MediaPipe
const initMediaPipe = async () => {
  let vision = await FilesetResolver.forVisionTasks(
    '/vision'
  );
  let vision2 = await FilesetResolver.forVisionTasks(
    '/vision'
  );
  try {
    // 初始化对象检测器
    objectDetector = await ObjectDetector.createFromOptions(vision, {
      baseOptions: {
        modelAssetPath: '/vision/efficientdet_lite0_float.tflite',
        delegate: 'GPU'
      },
      scoreThreshold: 0.5,
      maxResults: 5,
      categoryAllowlist: ['person']
    });
    faceDetector = await FaceDetector.createFromOptions(
    vision2,
    {
      baseOptions: {
        modelAssetPath: "/vision/blaze_face_short_range.tflite"
      }
    });
    // 初始化人脸检测器
    // faceDetector = await FaceLandmarker.createFromOptions(vision,{
    //     baseOptions: {
    //         modelAssetPath: '/vision/face_landmarker.task',
    //         delegate: 'GPU'
    //     },
    //     numFaces: 1,
    //     runningMode: 'VIDEO'
    // });

    console.log('MediaPipe 初始化完成');
  } catch (error) {
    console.error('MediaPipe 初始化失败:', error);
  }
};
// 在 sendMessage 函数中
const sendMessage = async (value) => {
  try {
    console.log('发送消息:', value)
    let result = await window.api.sendUdpMessage(value)
  } catch (error) {
    console.error('发送消息失败:', error)
  }
}
// 绘制检测结果
const drawDetections = (detections, _faceR) => {

  _faceR = _faceR || {}
  const canvas = canvasRef.value;
  const ctx = canvas.getContext('2d', { willReadFrequently: true });

  // 清除上一帧的绘制内容
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  if (detections.length === 0) {
    // console.log('未检测到人脸');
    return;
  } else {
    changeStatus('discorver')
    //  要求广角驱动摄像头
    let bound = detections[0].boundingBox
    let left = bound.originX + bound.width/2
    let _left = 140*(left/1920)-70
    _left = _left.toFixed(2)
    // sendMessage('ptz_rot:'+_left)
  }
  // 绘制每个检测框
  detections.forEach((detection, index) => {
    // console.log(detection)
    const box = detection.boundingBox;

    // 绘制边界框
    ctx.strokeStyle = '#00ff00';
    ctx.lineWidth = 2;
    ctx.strokeRect(
      box.originX,
      box.originY,
      box.width,
      box.height
    );

    // 绘制标签
    ctx.fillStyle = '#00ff00';
    ctx.font = '25px Arial';
    let _face = _faceR[index] || {}
    ctx.fillText(
      `${detection.categories[0].categoryName} ${Math.round(detection.categories[0].score * 100)}%|sex:${_face.gender == 'male' ? '男' : '女'}|age:${Math.round(_face.age)}|可信:${Math.round(_face.genderProbability * 100)}`,
      box.originX,
      box.originY - 5
    );
  });
};

const startDetection = () => {
  // 确保 canvas 尺寸与视频一致
  const canvas = canvasRef.value;

  const detectFrame = async () => {
    if (videoGRef.value && videoGRef.value.readyState === 4) {
      // 更新 canvas 尺寸
      if (canvas.width != videoGRef.value.videoWidth || canvas.height != videoGRef.value.videoHeight) {
        canvas.width = videoGRef.value.videoWidth;
        canvas.height = videoGRef.value.videoHeight;
      }
      let _faceR = await runFaceApi()
      try {
        // 直接使用 MediaPipe 进行对象检测
        if (objectDetector) {
          const detectionResult = objectDetector.detect(videoGRef.value);

          drawDetections(detectionResult.detections, _faceR);
        }
        
        // 可以同时进行人脸检测
        // faceapi, 变量名被占用
        // if (faceDetector) {
        //   const timestamp = performance.now();
        //   const faceResult = faceDetector.detectForVideo(videoGRef.value, timestamp);
        //   console.log('人脸检测结果:', faceResult);
        //   // 这里可以添加绘制人脸关键点的代码
        // }
      } catch (error) {
        console.error('检测过程中出错:', error);
      }
    }

    // 使用 requestAnimationFrame 进行下一帧检测
    // animationFrameId = requestAnimationFrame(detectFrame);

    let nextRenderTimeout = setTimeout(e => {
      detectFrame()
    }, 200)
  };

  detectFrame();
};

// 组件卸载时清理
onUnmounted(() => {
  if (nextRenderTimeout) {
    clearTimeout(nextRenderTimeout)
  }
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }

  // 释放 MediaPipe 资源
  if (objectDetector) {
    objectDetector.close();
    objectDetector = null;
  }

  if (faceDetector) {
    faceDetector.close();
    faceDetector = null;
  }
});

// 添加绘制人脸框的方法
const drawFaceBox = (x1, y1, x2, y2) => {
  if (!faceBoxCanvasRef.value || !videoRef.value) return;
  
  const canvas = faceBoxCanvasRef.value;
  const ctx = canvas.getContext('2d');
  
  // 确保canvas尺寸与视频一致
  if (canvas.width !== videoRef.value.videoWidth || canvas.height !== videoRef.value.videoHeight) {
    canvas.width = videoRef.value.videoWidth;
    canvas.height = videoRef.value.videoHeight;
  }
  
  // 清除上一帧的绘制内容
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  // 绘制边界框
  ctx.strokeStyle = '#ff0000'; // 使用红色区分与检测框
  ctx.lineWidth = 3;
  ctx.strokeRect(x1, y1, x2 - x1, y2 - y1);
  
  // 绘制标签
  ctx.fillStyle = '#ff0000';
  ctx.font = '25px Arial';
  ctx.fillText('人脸', x1, y1 - 10);
  
  // 保存边框内的内容为图片文件
  // let hasface = saveFaceBoxImage(x1, y1, x2, y2);
  
  // 设置定时器，3秒后清除绘制内容
  setTimeout(() => {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
  }, 3000);
};

const testFace = async (base64Image) => {
  if (faceDetector) {
    try {
      // 创建图片元素并加载 base64 数据
      const img = document.createElement('img')
      img.src = base64Image
      
      // 等待图片加载完成
      await new Promise((resolve) => {
        img.onload = resolve
      })
      
      // 使用 MediaPipe 进行人脸检测
      const detectionResult = await faceDetector.detect(img)
      console.log('人脸检测结果:', detectionResult)
      
      // 返回是否检测到人脸
      return detectionResult.detections.length > 0
    } catch (error) {
      console.error('人脸检测失败:', error)
      return false
    }
  }
  return false
}
// 新增保存人脸框内容为图片文件的方法
const saveFaceBoxImage = (x1, y1, x2, y2) => {
  if (!videoRef.value || videoRef.value.readyState !== 4) {
    console.error('视频未准备好');
    return;
  }
  // 创建临时canvas用于截图
  const tempCanvas = document.createElement('canvas');
  const width = x2 - x1;
  const height = y2 - y1;

  tempCanvas.width = width;
  tempCanvas.height = height;

  const ctx = tempCanvas.getContext('2d');

  // 从视频中截取指定区域
  ctx.drawImage(
    videoRef.value,
    x1, y1, width, height,  // 源图像的裁剪区域
    0, 0, width, height     // 目标canvas的绘制区域
  );

  
    // const detectionResult = faceDetector.detect(tempCanvas);
    // console.log(detectionResult)
    // if (detectionResult.detections.length === 0) {
    //   console.log('未检测到人脸');
    //   return false
    // }
  
  // 转换为base64
  const base64Image = tempCanvas.toDataURL('image/jpeg', 0.9);
  
  // 创建时间戳作为文件名的一部分
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  // 使用Electron的API保存文件
  try {
    window.api.saveFaceImage({
      filename: `face_${timestamp}.jpg`,
      data: base64Image
    }).then(result => {
      console.log('人脸图像已保存:', result);
    }).catch(error => {
      console.error('保存人脸图像失败:', error);
    });
  } catch (error) {
    console.error('调用保存API失败:', error);
  }
  
  console.log('已截取并保存人脸图像，尺寸:', width, 'x', height);
  return true;
};

// 添加截图方法
const captureImageFromBox = (x1, y1, x2, y2) => {
  if (!videoRef.value || videoRef.value.readyState !== 4) {
    console.error('视频未准备好');
    return null;
  }

  // 在截图前绘制人脸框
  drawFaceBox(x1, y1, x2, y2);

  // 创建临时canvas用于截图
  const tempCanvas = document.createElement('canvas');
  const width = x2 - x1;
  const height = y2 - y1;

  tempCanvas.width = width;
  tempCanvas.height = height;

  const ctx = tempCanvas.getContext('2d');

  // 从视频中截取指定区域
  ctx.drawImage(
    videoRef.value,
    x1, y1, width, height,  // 源图像的裁剪区域
    0, 0, width, height     // 目标canvas的绘制区域
  );

  // 转换为base64
  const base64Image = tempCanvas.toDataURL('image/jpeg', 0.9);
  console.log('已截取人脸图像，尺寸:', width, 'x', height);

  return base64Image;
};

// 暴露方法给父组件
// 修改 defineExpose，添加 testFace 方法
defineExpose({
  captureImageFromBox,
  drawFaceBox,
  saveFaceBoxImage,
  testFace // 暴露人脸检测方法
});
</script>
<script>
export default {
  name: 'CameraComponent',
  data() {
    return {
      stream: null,
      steamJ: null,
      imgUrl: '',
      devices: [],
      currentDeviceIndex: 0,
      hasMutipleCamera: false
    }
  },
  mounted() {
    this.initCamera()
    this.checkDevices()
  },
  beforeUnmount() {
    this.stopCamera()
  },
  methods: {
    async checkDevices() {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices()
        this.devices = devices.filter(device => device.kind === 'videoinput')
        this.hasMutipleCamera = this.devices.length > 1
      } catch (error) {
        console.error('获取设备列表失败:', error)
      }
    },
    async initCamera() {
      let allDevices = await navigator.mediaDevices.enumerateDevices();
      var cameraDevices = [];
      for (var i = 0; i < allDevices.length; i++) {
        var device = allDevices[i];
        if (device.kind == 'videoinput') {
          cameraDevices.push(device);
        }
      }
      try {
        // 查找包含 imx291 的摄像头,广角
        const targetDevice = cameraDevices.find(device =>
          device.label.toLowerCase().includes('imx291')
        );
        console.log(cameraDevices)
        const constraints = {
          video: {
            width: { ideal: 1920 },
            height: { ideal: 1080 },
            deviceId: targetDevice ? targetDevice.deviceId : this.devices[this.currentDeviceIndex]?.deviceId
          }
        }

        this.stream = await navigator.mediaDevices.getUserMedia(constraints)
        if (this.$refs.videoGRef) {
          this.$refs.videoGRef.srcObject = this.stream
        }
        // 截图摄像头
        const targetDeviceJ = cameraDevices.find(device =>{
          return device.label.toLowerCase().includes('2292')
        }
        );
        const constraintsJ = {
          video: {
            width: { ideal: 1920 },
            height: { ideal: 1080 },
            deviceId: targetDeviceJ ? targetDeviceJ.deviceId : this.devices[this.currentDeviceIndex]?.deviceId
          }
        }

        this.streamJ = await navigator.mediaDevices.getUserMedia(constraintsJ)
        if (this.$refs.videoRef) {
          this.$refs.videoRef.srcObject = this.streamJ
        }
      } catch (error) {
        console.error('摄像头初始化失败:', error)
      }
    },
    stopCamera() {
      if (this.stream) {
        this.stream.getTracks().forEach(track => track.stop())
      }
      if (this.streamJ) {
        this.streamJ.getTracks().forEach(track => track.stop())
      }
    },
    takeSnapshot() {
      const video = this.$refs.videoRef
      const canvas = document.createElement('canvas')
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      const ctx = canvas.getContext('2d')
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

      this.imgUrl = canvas.toDataURL('image/png')
    },
    downloadImage() {
      if (!this.imgUrl) return

      const link = document.createElement('a')
      link.href = this.imgUrl
      link.download = `snapshot-${new Date().toISOString()}.png`
      link.click()
    },
    closeSnapshot() {
      this.imgUrl = ''
    },
    async switchCamera() {
      this.stopCamera()
      this.currentDeviceIndex = (this.currentDeviceIndex + 1) % this.devices.length
      await this.initCamera()
    }
  }
}
</script>
<style scoped>
/* 检测提示 */
.detection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  /* height: 100%; */
  pointer-events: none;
}

.face-box-canvas {
  position: absolute;
  /* top: 0; */
  left: 0;
  width: 100%;
  pointer-events: none;
  z-index: 2; /* 确保人脸框在检测框之上 */
}

.camera-container {
  position: relative;
  width: 100%;
  max-width: 640px;
  margin: 0 auto;
}

.camera-video {
  width: 100%;
  /* border-radius: 8px; */
  background-color: #000;
}

.camera-controls {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  gap: 16px;
}

.snapshot-btn,
.switch-btn,
.download-btn,
.close-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #4CAF50;
  color: white;
  cursor: pointer;
  font-size: 16px;
}

.switch-btn {
  background-color: #2196F3;
}

.close-btn {
  background-color: #f44336;
}

.snapshot-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.snapshot-img {
  max-width: 90%;
  max-height: 70vh;
  border: 2px solid white;
}

.snapshot-actions {
  margin-top: 16px;
  display: flex;
  gap: 16px;
}
</style>