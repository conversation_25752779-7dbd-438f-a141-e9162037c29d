<html>
<head>
	<title><PERSON><PERSON></title>

	<meta charset="utf-8">

	<script src="gl-matrix-min.js"></script>
	<!--https://www.tamats.com/projects/webglstudio/editor/js/extra/-->
	<script src="litegl.js"></script>
	<script src="litescene.js"></script>
	<script src="Canvas2DtoWebGL.js"></script>
	
</head>

<body>
	
	<div id = "container">
	</div>

	<script>
		var player = new LS.Player({
		    resources: "resources/",
		    shaders: "shaders.xml"
		});

		document.getElementById("container").appendChild(player.canvas);

		player.loadScene("emmaLipSync.SCENE.wbin");
		player.canvas.setAttribute("margin", "auto");

		document.onkeypress = function(e){
			if (e.keyCode == 49)
				player.loadScene("emmaLipSync.SCENE.wbin");
			else if (e.keyCode == 50)
				player.loadScene("hermannLipSync.SCENE.wbin");
			else if (e.keyCode == 50)
				player.loadScene("emmaLipSync.SCENE.wbin");
			else if (e.keyCode == 50)
				player.loadScene("emmaLipSync.SCENE.wbin");
		}
		
	</script>


</body>
</html>