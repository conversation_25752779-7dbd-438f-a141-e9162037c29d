export class CatStatus {
    statusMap = ['idle', 'discorver', 'shooting', 'catch', 'start', 'success', 'error']
    nowStatus = 0
    tempStatus = []
    func = null
    constructor(func) {
        this.nowStatus = 0;
        this.func = func;
    }
    changeStatus(status) {
        nowStatus = status
        if (status === 'start'){
          
        }
        if (status ==='success' || status ==='error') {
          if (status == 'start'){
            status = 'idle'
          }
          return
        }
        console.log('HelloWorld收到状态变化:', status)
        
        // 根据状态执行不同的song方法
        if (status === 'discorver') {
          song('zhaoren') // 发现人时播放"摇人"语音
        } else if (status === 'shooting') {
          song('yindao') // 拍摄时播放"引导"语音
        }else if (status === 'catch') {
          song('paizhao') // 捕获人脸时播放"拍照"语音
        } 
    }

}